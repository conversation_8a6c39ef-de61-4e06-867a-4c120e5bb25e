# Signalsd Testing

## Unit Tests 

Unit tests are used to test a couple of areas:
- `app/internal/server/utils/utils_test.go` - URL validation and SSRF protection (ensures user submitted URLs are only GitHub URLs)
- `app/internal/server/request_limits_test.go` - Rate limiting and request size controls

## Integration Tests

The integration tests are designed to ensure that signal data is handled correctly and that authentication, authorization and privacy controls work as intended.

**integration test helper files `app/test/integration/`:**

- `setup_test_env.go` - Test environment setup and server lifecycle management
- `database.go` - database query test helpers

### 1. Authentication & Authorization (`auth_test.go`)

these tests verify the behaviour of the authentication and authorization system by running the application database queries directly and inspecting the tokens generated by the auth service. 

**What it tests:**

- ✅ JWT token structure - tokens are properly signed and parseable
- ✅ JWT claims (ISN permissions, role etc) match database 
- ✅ Token metadata - expiration, issued time, issuer, subject are correct
- ✅ Role-based permissions - owner gets write to all ISNs, admin gets write to own ISNs
- ✅ Explicit permission grants - member gets read or write access where granted
- ✅ Service account batch handling - service accounts require batch IDs for write permissions
- ✅ Signal type paths - correct signal type paths are included in permissions
- ✅ Disabled account handling
- ✅ Login - password validation, account status checks, access/refresh token generation, refresh token rotation
- ✅ service account authentication - client credentials validation, revoked and expired secrets


### 2. OAuth Endpoints (`oauth_test.go`)

These tests verify the behaviour of the OAuth endpoints by making HTTP requests to the server and inspecting the responses.

**What it tests:**

***Token Generation (/oauth/token)***
- ✅ Client credentials grant (service accounts) - valid/invalid client_id and client_secret
- ✅ Refresh token grant (web users) - valid/invalid refresh token cookies with access tokens
- ✅ Response structure validation (access_token, token_type, error_code fields)
- ✅ Cookie handling for refresh token rotation

***Token Revocation (/oauth/revoke)***
- ✅ Service account credential revocation
- ✅ Web user token revocation 
- ✅ Invalid credential handling and proper error responses
- ✅ Cross-account security (accounts cannot revoke each other's tokens)


### 3.  Signals Creation/Search Endpoints (`signal_test.go`)

These tests verify the behaviour of the signals exchange endpoints by making HTTP requests to the server and inspecting the responses.

**What it tests:**

***Signals Creation:***
 - ✅ Successful submission
 - ✅ Failed submission due to validation errors
 - ✅ Failed submission due to authorization errors
 - ✅ Failed submission due to request errors (e.g. invalid JSON)
 - ✅ Signal versioning and reactivation of withdrawn signals
 - ✅ loading signals with Correlation IDs
 - ✅ Multi-signal payload processing with mixed success/failure scenarios

***Signal Search:***
 - ✅ Search results (with and without correlated signals)
 - ✅ Authorization errors
 - ✅ Request errors (e.g. invalid JSON)
 - ✅ Response structure and error response validation
 - ✅ public/priviate ISN visibility
 - ✅ Verifies withdrawn signals are excluded from search results by default
 - ✅ Tests that withdrawn signals can be included when explicitly requested


**Privacy & Security:**
- ✅ Tests that unauthorized users cannot submit or view signals on private ISNs
- ✅ Ensures proper error handling and correctly structured error responses
- ✅ Verifies private ISNs are not accessible via public endpoints
- ✅ Tests CORS configuration prevents unauthorized cross-origin access
- ✅ Middleware authentication and authorization functionality


### 4. Service Account Batch Management (`batch_test.go`)

**What it tests:**
- ✅ Batch creation and automatic closure when new batch is created
- ✅ Service account signal submission requirements (must have active batch)
- ✅ Batch validation and error handling

**TODO**
  - **rate limiting integration tests** - unit tests exist, but actual behaviour is only tested indirectly (see perf tests, which can be set up to trigger the rate limiter)
  - **ISN admin endpoints** - although the underlying auth functionality is tested, there are no end-to-end HTTP tests for ISN administration endpoints (this is mainly JSON handling - manual testing needed when changing these handlers)
  - **test performance** - each test sets up a fresh database and applies all the migrations so the schema reflects the latest code.  This is convenient - because the tests are guaranteed to be isolated - but not very efficient.  The biggest contributor to slowness is the credential hashing in the login tests (if an issue consider reduing the bcrypt factor when testing).

### 5. CORS (`cors_test.go`)

**What it tests:**
- ✅ Allowed origins are correctly configured and enforced
- ✅ Untrusted origins are blocked
- ✅ Public endpoints are accessible by all origins
- ✅ Protected endpoints respect the configured CORS policy

## Running the tests
```bash
# Start the development database
docker compose up db

# run tests from app directory
cd app

# Run integration tests
go test -tags=integration ./test/integration/

# Enable detailed HTTP request/response logging
ENABLE_SERVER_LOGS=true go test -v -tags=integration ./test/integration/

# Run unit tests
go test ./...
```

### Test Environment
- **Local**: Uses the dev Docker Compose PostgreSQL container (port 15432)
- **CI**: Uses a GitHub Actions PostgreSQL service (port 5432)
- Each integration test creates a temporary database (`tmp_signalsd_integration_test`) and applies the migrations so the schema reflects the latest code
-  end-to-end HTTP tests also start the signalsd service on a random available port
- Database and server are cleaned up after each test completes
